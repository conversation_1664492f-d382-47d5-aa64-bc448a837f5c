#include "stdint.h"
#include "stdio.h"
#include "CMIC_GD32f470vet6.h"

// 使用联合体实现浮点数与IEEE 754格式的转换
typedef union {
    float f;
    uint32_t u;
} Float_IEEE754;

// 浮点数转换为IEEE 754格式
uint32_t floatToIEEE754(float value) {
    Float_IEEE754 converter;
    converter.f = value;
    return converter.u;
}


void  IEEE_test(void) 
{
    float testValue = 134150;

    // 转换为IEEE 754格式
    uint32_t ieee = floatToIEEE754(testValue);

	rs485_printf("RS485:0x%08x\r\n",ieee);
}



