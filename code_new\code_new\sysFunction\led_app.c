#include "CMIC_GD32f470vet6.h"
uint8_t UCLed[6] = {1,0,1,0,1,0};  
void led_Read(uint8_t *UCLed)
{
    uint8_t temp1 = 0x00;
    static uint8_t temp_old1 = 0xff;
    for (int i = 0; i < 6; i++)
    {
       if (UCLed[i]) temp1 |= (1<<i); 
    }
    if (temp_old1 != temp1)
    {
        LED1_SET(temp1 & 0x01);
        LED1_SET(temp1 & 0x02);
        LED1_SET(temp1 & 0x04);
        LED1_SET(temp1 & 0x08);
        LED1_SET(temp1 & 0x10);
        LED1_SET(temp1 & 0x20);
        temp_old1 = temp1;
    }
}
void led_Proc(void)
{
    led_Read(UCLed);
}

