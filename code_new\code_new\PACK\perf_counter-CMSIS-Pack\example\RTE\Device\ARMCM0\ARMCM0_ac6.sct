#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m0 -xc
; command above MUST be in first line (no comment above!)

/*
;-------- <<< Use Configuration Wizard in Context Menu >>> -------------------
*/

/*--------------------- Flash Configuration ----------------------------------
; <h> Flash Configuration
;   <o0> Flash Base Address <0x0-0xFFFFFFFF:8>
;   <o1> Flash Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>
 *----------------------------------------------------------------------------*/
#define __ROM_BASE      0x00000000
#define __ROM_SIZE      0x00080000

/*--------------------- Embedded RAM Configuration ---------------------------
; <h> RAM Configuration
;   <o0> RAM Base Address    <0x0-0xFFFFFFFF:8>
;   <o1> RAM Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>
 *----------------------------------------------------------------------------*/
#define __RAM_BASE      0x20000000
#define __RAM_SIZE      0x00040000

/*--------------------- Stack / Heap Configuration ---------------------------
; <h> Stack / Heap Configuration
;   <o0> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
;   <o1> Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>
 *----------------------------------------------------------------------------*/
#define __STACK_SIZE    0x00000200
#define __HEAP_SIZE     0x00000C00

/*
;------------- <<< end of configuration section >>> ---------------------------
*/


/*----------------------------------------------------------------------------
  User Stack & Heap boundary definition
 *----------------------------------------------------------------------------*/
#define __STACK_TOP    (__RAM_BASE + __RAM_SIZE)    /* starts at end of RAM */
#define __HEAP_BASE    (AlignExpr(+0, 8))           /* starts after RW_RAM section, 8 byte aligned */


/*----------------------------------------------------------------------------
  Scatter File Definitions definition
 *----------------------------------------------------------------------------*/
#define __RO_BASE       __ROM_BASE
#define __RO_SIZE       __ROM_SIZE

#define __RW_BASE       __RAM_BASE
#define __RW_SIZE      (__RAM_SIZE - __STACK_SIZE - __HEAP_SIZE)


LR_ROM __RO_BASE __RO_SIZE  {                       ; load region size_region
  ER_ROM __RO_BASE __RO_SIZE  {                     ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   .ANY (+XO)
  }

  RW_NOINIT __RW_BASE UNINIT __RW_SIZE {
    *(.bss.noinit)
  }

  RW_RAM AlignExpr(+0, 8) (__RW_SIZE - AlignExpr(ImageLength(RW_NOINIT), 8)) {
    *(+RW +ZI)
  }

#if __HEAP_SIZE > 0
  ARM_LIB_HEAP  __HEAP_BASE EMPTY  __HEAP_SIZE  {   ; Reserve empty region for heap
  }
#endif

  ARM_LIB_STACK __STACK_TOP EMPTY -__STACK_SIZE {   ; Reserve empty region for stack
  }
}
