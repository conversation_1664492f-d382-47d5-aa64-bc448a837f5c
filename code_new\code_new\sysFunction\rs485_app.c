#include "CMIC_GD32f470vet6.h"


volatile uint8_t rs485_rx_flag = 0;
extern uint8_t rs485_rxbuffer[512];
extern uint8_t rs485_dma_buffer[512];

//extern float ratio_value_ch0;
//extern float ratio_value_ch1;
//extern float ratio_value_ch2;
//extern float limit_value_ch0;
//extern float limit_value_ch1;
//extern float limit_value_ch2;

// RS485发送函数
int rs485_printf(const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    
    // 格式化字符串
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    // 切换到发送模式
    rs485_set_tx_mode();
    //delay_1ms(1);  // 等待切换完成
    
    // 发送数据
    for(int i = 0; i < len; i++){
        usart_data_transmit(RS485_USART, buffer[i]);
        while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TBE));
    }
    
    // 等待发送完成
    while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TC));
    //delay_1ms(1);
    
    // 切换回接收模式
    rs485_set_rx_mode();
    
    return len;
}

// RS485发送固定字符串
void rs485_send_string(const char *str)
{
    rs485_set_tx_mode();
    //delay_1ms(1);
    
    while(*str) {
        usart_data_transmit(RS485_USART, *str);
        while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TBE));
        str++;
    }
    
    while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TC));
    //delay_1ms(1);
    rs485_set_rx_mode();
}

// RS485接收处理
void rs485_task(void)
{
    // 需要的外部变量声明

    if(rs485_rx_flag) 
			{
        rs485_rx_flag = 0;
            // 简单回显测试
            rs485_printf("RS485: %s", rs485_dma_buffer);
        }

        // 清空缓冲区
        memset(rs485_dma_buffer, 0, sizeof(rs485_dma_buffer));
    
}



